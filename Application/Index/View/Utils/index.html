<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密购买 - {$con.webname}</title>
    <link rel="icon" href="__PUBLIC__/static/annie/img/fovicon.ico">
    <meta name="description" content="购买会员卡密，享受更多服务">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 14px;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .products {
            padding: 40px;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .product-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: #667eea;
        }

        .product-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .product-price {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            text-align: center;
            margin-bottom: 15px;
        }

        .product-price .currency {
            font-size: 0.6em;
            vertical-align: top;
        }

        .product-features {
            list-style: none;
            margin-bottom: 25px;
        }

        .product-features li {
            padding: 8px 0;
            color: #666;
            position: relative;
            padding-left: 25px;
        }

        .product-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }

        .buy-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .buy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .popular {
            position: relative;
        }

        .popular::after {
            content: '推荐';
            position: absolute;
            top: -10px;
            right: -10px;
            background: #FF6B6B;
            color: white;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            transform: rotate(15deg);
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            position: relative;
        }

        .modal-header h2 {
            margin: 0;
            text-align: center;
        }

        .close {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.3s ease;
        }

        .close:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 30px;
            text-align: center;
        }

        .payment-options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .payment-option {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .payment-option:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .payment-option.selected {
            border-color: #667eea;
            background-color: #667eea;
            color: white;
        }

        .payment-icon {
            width: 24px;
            height: 24px;
        }

        .qr-container {
            margin: 20px 0;
        }

        .qr-code {
            display: inline-block;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .payment-info {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9ff;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .confirm-btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .confirm-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }

        .confirm-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-icon {
            color: #4CAF50;
            font-size: 3em;
            margin-bottom: 20px;
        }

        .error-icon {
            color: #f44336;
            font-size: 3em;
            margin-bottom: 20px;
        }

        .token-display {
            background: #f8f9ff;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            word-break: break-all;
        }

        .warning-text {
            color: #ff6b6b;
            font-size: 0.9em;
            margin-top: 15px;
            padding: 10px;
            background: #fff5f5;
            border-radius: 5px;
            border-left: 4px solid #ff6b6b;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .products {
                padding: 20px;
            }

            .product-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
            }

            .payment-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/" class="back-btn">← 返回首页</a>
            <h1>🎫 会员卡密购买</h1>
            <p>选择适合您的会员套餐，享受更多优质服务</p>
        </div>

        <div class="products">
            <div class="product-grid">
                <!-- 月度卡密 -->
                <div class="product-card">
                    <div class="product-title">月度会员卡密</div>
                    <div class="product-price">
                        <span class="currency">¥</span>28.05
                    </div>
                    <ul class="product-features">
                        <li>买一月送一周</li>
                        <li>共计38天使用期</li>
                        <li>享受所有会员功能</li>
                        <li>优先客服支持</li>
                    </ul>
                    <button class="buy-btn" onclick="selectProduct(85, '月度会员卡密', '28.05')">立即购买</button>
                </div>

                <!-- 永久卡密 -->
                <div class="product-card popular">
                    <div class="product-title">永久会员卡密</div>
                    <div class="product-price">
                        <span class="currency">¥</span>38.05
                    </div>
                    <ul class="product-features">
                        <li>加10元得永久</li>
                        <li>终身使用权限</li>
                        <li>永不到期</li>
                        <li>所有功能无限制</li>
                        <li>VIP专属客服</li>
                    </ul>
                    <button class="buy-btn" onclick="selectProduct(86, '永久会员卡密', '38.05')">立即购买</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 支付方式选择模态框 -->
    <div id="paymentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>选择支付方式</h2>
                <span class="close" onclick="closeModal('paymentModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="payment-info">
                    <strong id="selectedProduct"></strong><br>
                    价格: ¥<span id="selectedPrice"></span>
                </div>

                <div class="payment-options">
                    <div class="payment-option" onclick="selectPayment('wxpay')">
                        <span class="payment-icon">💬</span>
                        <span>微信支付</span>
                    </div>
                    <div class="payment-option" onclick="selectPayment('alipay')">
                        <span class="payment-icon">💰</span>
                        <span>支付宝</span>
                    </div>
                </div>

                <button class="confirm-btn" id="confirmPayment" onclick="createOrder()" disabled>
                    确认支付方式
                </button>
            </div>
        </div>
    </div>

    <!-- 支付二维码模态框 -->
    <div id="qrModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>扫码支付</h2>
                <span class="close" onclick="closeModal('qrModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="payment-info">
                    <strong>订单号: <span id="orderId"></span></strong><br>
                    商品: <span id="orderProduct"></span><br>
                    金额: ¥<span id="orderPrice"></span>
                </div>

                <div class="qr-container">
                    <div class="qr-code" id="qrCode"></div>
                </div>

                <p>请使用<span id="paymentMethod"></span>扫描上方二维码完成支付</p>

                <button class="confirm-btn" id="checkPayment" onclick="checkPaymentStatus()">
                    <span id="checkText">我已支付</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 支付结果模态框 -->
    <div id="resultModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="resultTitle">支付结果</h2>
                <span class="close" onclick="closeModal('resultModal')">&times;</span>
            </div>
            <div class="modal-body" id="resultBody">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <!-- 引入二维码生成库 -->
    <script src="__PUBLIC__/js/qrcode.js"></script>
    <script>
        let currentProduct = null;
        let currentPaymentType = null;
        let currentOrderId = null;
        let checkingPayment = false;

        // 选择商品
        function selectProduct(productId, productName, price) {
            currentProduct = {
                id: productId,
                name: productName,
                price: price
            };

            document.getElementById('selectedProduct').textContent = productName;
            document.getElementById('selectedPrice').textContent = price;

            // 重置支付方式选择
            currentPaymentType = null;
            document.querySelectorAll('.payment-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.getElementById('confirmPayment').disabled = true;

            showModal('paymentModal');
        }

        // 选择支付方式
        function selectPayment(paymentType) {
            currentPaymentType = paymentType;

            document.querySelectorAll('.payment-option').forEach(option => {
                option.classList.remove('selected');
            });

            event.target.closest('.payment-option').classList.add('selected');
            document.getElementById('confirmPayment').disabled = false;
        }

        // 创建订单
        function createOrder() {
            if (!currentProduct || !currentPaymentType) {
                alert('请选择商品和支付方式');
                return;
            }

            const confirmBtn = document.getElementById('confirmPayment');
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<span class="loading"></span>创建订单中...';

            // 调用支付API创建订单
            const params = new URLSearchParams({
                customer_contact: '<EMAIL>', // 这里可以让用户输入或使用默认值
                product_id: currentProduct.id,
                pay_type: currentPaymentType
            });

            fetch(`https://cloudshop.qnm6.top/create_order.php?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        currentOrderId = data.data.order_info.order_id;

                        // 更新订单信息
                        document.getElementById('orderId').textContent = currentOrderId;
                        document.getElementById('orderProduct').textContent = data.data.order_info.product_name;
                        document.getElementById('orderPrice').textContent = data.data.order_info.product_price;
                        document.getElementById('paymentMethod').textContent = currentPaymentType === 'wxpay' ? '微信' : '支付宝';

                        // 生成二维码
                        generateQRCode(data.data.payment_url);

                        // 关闭支付方式选择模态框，显示二维码模态框
                        closeModal('paymentModal');
                        showModal('qrModal');
                    } else {
                        alert('创建订单失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('创建订单错误:', error);
                    alert('创建订单失败，请重试');
                })
                .finally(() => {
                    confirmBtn.disabled = false;
                    confirmBtn.innerHTML = '确认支付方式';
                });
        }

        // 生成二维码
        function generateQRCode(url) {
            const qrContainer = document.getElementById('qrCode');
            qrContainer.innerHTML = '';

            try {
                const qr = qrcode(0, 'M');
                qr.addData(url);
                qr.make();

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const modules = qr.getModuleCount();
                const cellSize = 300 / modules;

                canvas.width = 300;
                canvas.height = 300;

                // 设置背景色为白色
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, 300, 300);

                // 绘制二维码
                ctx.fillStyle = '#000000';
                for (let row = 0; row < modules; row++) {
                    for (let col = 0; col < modules; col++) {
                        if (qr.isDark(row, col)) {
                            ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                        }
                    }
                }

                qrContainer.appendChild(canvas);
            } catch (error) {
                console.error('生成二维码失败:', error);
                qrContainer.innerHTML = '<p style="color: red;">二维码生成失败</p>';
            }
        }

        // 检查支付状态
        function checkPaymentStatus() {
            if (!currentOrderId || checkingPayment) {
                return;
            }

            checkingPayment = true;
            const checkBtn = document.getElementById('checkPayment');
            const checkText = document.getElementById('checkText');

            checkBtn.disabled = true;
            checkText.innerHTML = '<span class="loading"></span>检查支付状态...';

            fetch(`https://cloudshop.qnm6.top/check_payment_status.php?order_id=${currentOrderId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.data.order_status === 'paid') {
                        // 支付成功，调用会员注册API
                        registerVip();
                    } else {
                        // 未支付
                        showNotification('未检测到支付，请确认支付后再试', 'warning');
                    }
                })
                .catch(error => {
                    console.error('检查支付状态错误:', error);
                    showNotification('检查支付状态失败，请重试', 'error');
                })
                .finally(() => {
                    checkingPayment = false;
                    checkBtn.disabled = false;
                    checkText.textContent = '我已支付';
                });
        }

        // 注册会员
        function registerVip() {
            const lx = currentProduct.id === 85 ? '1' : '2';

            fetch(`https://api.qnm6.top/admapi/vipregister.php?lx=${lx}`)
                .then(response => response.json())
                .then(data => {
                    closeModal('qrModal');

                    if (data.code === 200) {
                        // 注册成功
                        showSuccessResult(data.token, data.reg_time, data.expire_time);
                    } else {
                        // 注册失败
                        showErrorResult(data.message || '注册失败');
                    }
                })
                .catch(error => {
                    console.error('注册会员错误:', error);
                    closeModal('qrModal');
                    showErrorResult('注册失败，请联系客服');
                });
        }

        // 显示成功结果
        function showSuccessResult(token, regTime, expireTime) {
            const resultBody = document.getElementById('resultBody');
            document.getElementById('resultTitle').textContent = '注册成功';

            resultBody.innerHTML = `
                <div class="success-icon">✅</div>
                <h3>恭喜！会员注册成功</h3>
                <div class="payment-info">
                    <strong>订单号:</strong> ${currentOrderId}<br>
                    <strong>注册时间:</strong> ${regTime}<br>
                    <strong>到期时间:</strong> ${expireTime}
                </div>
                <div class="token-display">
                    您的卡密: ${token}
                </div>
                <div class="warning-text">
                    ⚠️ 请谨慎保存您的卡密，丢失后无法找回！建议截图保存。
                </div>
            `;

            showModal('resultModal');
        }

        // 显示错误结果
        function showErrorResult(message) {
            const resultBody = document.getElementById('resultBody');
            document.getElementById('resultTitle').textContent = '注册失败';

            resultBody.innerHTML = `
                <div class="error-icon">❌</div>
                <h3>注册失败</h3>
                <div class="payment-info">
                    <strong>订单号:</strong> ${currentOrderId}<br>
                    <strong>错误信息:</strong> ${message}
                </div>
                <p>请联系客服处理：</p>
                <a href="https://t.me/Dataso" target="_blank" style="color: #667eea; text-decoration: none; font-weight: bold;">
                    📱 Telegram客服: @Dataso
                </a>
            `;

            showModal('resultModal');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'error' ? '#f44336' : type === 'warning' ? '#ff9800' : '#4CAF50'};
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                font-weight: bold;
                max-width: 300px;
                animation: slideInRight 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 显示模态框
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 点击模态框外部不关闭（根据需求）
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    // 对于支付二维码、结果模态框，点击外部不关闭
                    if (modal.id === 'qrModal' || modal.id === 'resultModal') {
                        return;
                    }
                    // 只有支付方式选择模态框可以点击外部关闭
                    if (modal.id === 'paymentModal') {
                        closeModal(modal.id);
                    }
                }
            });
        }

        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>